#!/usr/bin/env python3
"""
Example usage of the Sonarr File Browser

This script demonstrates how to use the SonarrFileBrowser class
programmatically without the interactive interface.
"""

from sonarr_file_browser import SonarrFileBrowser


def example_usage():
    # Initialize the browser
    # Replace these with your actual Sonarr server details
    host = "localhost"
    port = 8989
    api_key = "your_api_key_here"  # Replace with your actual API key
    
    browser = SonarrFileBrowser(host, port, api_key)
    
    # Test connection
    print("Testing connection...")
    if not browser.test_connection():
        print("Failed to connect to Sonarr")
        return
    
    # Browse root directory
    print("\nBrowsing root directory:")
    items = browser.browse_filesystem(include_files=True)
    if items:
        browser.display_directory_listing(items, "Root")
    
    # Get episode files
    print("\nGetting episode files:")
    episodes = browser.get_episode_files()
    if episodes:
        print(f"Found {len(episodes)} episode files")
        for i, episode in enumerate(episodes[:5]):  # Show first 5
            print(f"  {i+1}. {episode.get('path', 'Unknown path')}")
        if len(episodes) > 5:
            print(f"  ... and {len(episodes) - 5} more")
    
    # Example of checking a specific path
    test_path = "/media"  # Change this to a path that exists on your system
    print(f"\nChecking path: {test_path}")
    file_type = browser.get_file_type(test_path)
    if file_type:
        print(f"Path type: {file_type}")
    
    # Example of getting media files in a directory
    print(f"\nGetting media files in: {test_path}")
    media_files = browser.get_media_files(test_path)
    if media_files:
        print(f"Found {len(media_files)} media files")
        for media_file in media_files[:3]:  # Show first 3
            name = media_file.get('name', 'Unknown')
            size = browser.format_size(media_file.get('size', 0))
            print(f"  - {name} ({size})")


if __name__ == "__main__":
    print("Sonarr File Browser - Example Usage")
    print("=" * 40)
    print("Make sure to update the host, port, and api_key variables")
    print("in this script before running it.")
    print()
    
    # Uncomment the line below after updating the credentials
    # example_usage()
    
    print("Please edit this file and update the connection details first!")
