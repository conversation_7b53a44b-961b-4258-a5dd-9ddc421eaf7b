# Sonarr File System Browser

A Python script that allows you to navigate and explore the file system of a Sonarr server using the Sonarr API. This tool provides an interactive command-line interface to browse directories, view files, and inspect media files managed by Sonarr.

## Features

- **Interactive file system navigation** - Browse directories like a command-line shell
- **File and directory listing** - View contents with size information and file types
- **Media file inspection** - Get detailed information about media files
- **Episode file management** - View all episode files tracked by Sonarr
- **File type detection** - Check if paths are files or directories
- **Secure API authentication** - Uses Sonarr API keys for secure access

## Requirements

- Python 3.6 or higher
- `requests` library
- Access to a Sonarr server with API enabled

## Installation

1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install requests
   ```

## Usage

### Basic Usage

```bash
python sonarr_file_browser.py <ip_address> [port] [api_key]
```

**Parameters:**
- `ip_address` - IP address of your Sonarr server (required)
- `port` - Port number (optional, defaults to 8989)
- `api_key` - Your Sonarr API key (optional, will prompt if not provided)

### Examples

```bash
# Connect to Sonarr on localhost with default port
python sonarr_file_browser.py localhost

# Connect to remote Sonarr server with custom port
python sonarr_file_browser.py ************* 8989

# Connect with API key provided
python sonarr_file_browser.py localhost 8989 your_api_key_here
```

### Getting Your API Key

1. Open Sonarr web interface
2. Go to Settings → General
3. Find the "API Key" field
4. Copy the key for use with this script

## Interactive Commands

Once connected, you can use these commands:

### Navigation Commands
- `ls [path]` - List directory contents (files and folders)
- `cd <path>` - Change to specified directory
- `pwd` - Show current directory path
- `cd ..` - Go up one directory level

### File Information Commands
- `view <file>` - View detailed information about a specific file
- `type <path>` - Show whether path is a file or directory
- `media [path]` - Show only media files in the specified path

### Sonarr-Specific Commands
- `episodes` - List all episode files tracked by Sonarr

### Utility Commands
- `help` - Show all available commands
- `quit`, `exit`, or `q` - Exit the browser

## Example Session

```
$ python sonarr_file_browser.py localhost
Enter Sonarr API key: ********************************
Connected to Sonarr ******* on http://localhost:8989

Sonarr File System Browser
Commands: ls [path], cd <path>, pwd, media [path], episodes, view <file>, help, quit
================================================================================

sonarr:/$ ls
Directory listing for: Root
--------------------------------------------------------------------------------
Type   Name                                     Size         Path
--------------------------------------------------------------------------------
DIR    media                                                 /media
DIR    downloads                                             /downloads
DIR    config                                                /config

sonarr:/$ cd media
Changed to: /media

sonarr:/media$ ls
Directory listing for: /media
--------------------------------------------------------------------------------
Type   Name                                     Size         Path
--------------------------------------------------------------------------------
DIR    TV Shows                                              /media/TV Shows
DIR    Movies                                                /media/Movies

sonarr:/media$ cd "TV Shows"
Changed to: /media/TV Shows

sonarr:/media/TV Shows$ media
Directory listing for: /media/TV Shows (media files)
--------------------------------------------------------------------------------
Type   Name                                     Size         Path
--------------------------------------------------------------------------------
FILE   show.s01e01.mkv                        1.2 GB       /media/TV Shows/Show/Season 1/show.s01e01.mkv
FILE   show.s01e02.mkv                        1.1 GB       /media/TV Shows/Show/Season 1/show.s01e02.mkv

sonarr:/media/TV Shows$ view "/media/TV Shows/Show/Season 1/show.s01e01.mkv"
File information for: /media/TV Shows/Show/Season 1/show.s01e01.mkv
--------------------------------------------------
type: file
size: 1234567890
name: show.s01e01.mkv
path: /media/TV Shows/Show/Season 1/show.s01e01.mkv

sonarr:/media/TV Shows$ episodes
Found 150 episode files:
--------------------------------------------------------------------------------
ID     Series               Season   Path
--------------------------------------------------------------------------------
1      <USER>                  <GROUP>        /media/TV Shows/Show/Season 1/show.s01e01.mkv
2      123                  1        /media/TV Shows/Show/Season 1/show.s01e02.mkv
...

sonarr:/media/TV Shows$ quit
```

## API Endpoints Used

This script utilizes the following Sonarr API v3 endpoints:

- `/api/v3/system/status` - Test connection and get server info
- `/api/v3/filesystem` - Browse filesystem and list directory contents
- `/api/v3/filesystem/type` - Get file/directory type information
- `/api/v3/filesystem/mediafiles` - Get media files in a specific path
- `/api/v3/episodefile` - Get episode file information

## Error Handling

The script includes comprehensive error handling for:
- Network connection issues
- Invalid API keys (401 authentication errors)
- Invalid paths or inaccessible directories
- Malformed API responses
- Keyboard interrupts (Ctrl+C)

## Security Notes

- API keys are handled securely and not displayed in terminal output
- The script uses HTTPS when available
- No file modification operations are performed (read-only access)

## Troubleshooting

**Connection Issues:**
- Verify Sonarr is running and accessible
- Check firewall settings
- Ensure API is enabled in Sonarr settings

**Authentication Issues:**
- Verify API key is correct
- Check that API key has necessary permissions
- Ensure Sonarr authentication settings allow API access

**Path Issues:**
- Use forward slashes (/) for paths on all platforms
- Enclose paths with spaces in quotes
- Use absolute paths when possible

## License

This script is provided as-is for educational and utility purposes. Please ensure you have proper authorization to access the Sonarr server you're connecting to.
