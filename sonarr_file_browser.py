#!/usr/bin/env python3
"""
Sonarr File System Browser

A Python script to navigate the Sonarr file system using the Sonarr API.
Allows browsing directories and viewing file information.

Usage:
    python sonarr_file_browser.py <ip_address> [port] [api_key] [--https]

    ip_address: IP address of the Sonarr server
    port: Port number (default: 8989)
    api_key: API key for authentication (will prompt if not provided)
    --https: Use HTTPS instead of HTTP
"""

import sys
import os
import requests
import json
from urllib.parse import urljoin, quote
import getpass
from typing import Dict, List, Optional, Any


class SonarrFileBrowser:
    def __init__(self, host: str, port: int = 8989, api_key: str = None, use_https: bool = False):
        self.host = host
        self.port = port
        self.use_https = use_https
        protocol = "https" if use_https else "http"
        self.base_url = f"{protocol}://{host}:{port}"
        self.api_key = api_key
        self.session = requests.Session()

        if self.api_key:
            self.session.headers.update({'X-Api-Key': self.api_key})
    
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make a request to the Sonarr API"""
        url = urljoin(self.base_url, endpoint)

        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                if e.response.status_code == 401:
                    print("Authentication failed. Please check your API key.")
            return None
    
    def test_connection(self) -> bool:
        """Test connection to Sonarr API"""
        result = self._make_request('/api/v3/system/status')
        if result:
            print(f"Connected to Sonarr {result.get('version', 'Unknown')} on {self.base_url}")
            return True
        return False
    
    def browse_filesystem(self, path: str = None, include_files: bool = True) -> Optional[List[Dict]]:
        """Browse the filesystem at the given path"""
        params = {
            'includeFiles': include_files,
            'allowFoldersWithoutTrailingSlashes': True
        }
        
        if path:
            params['path'] = path
        
        return self._make_request('/api/v3/filesystem', params)
    
    def get_file_type(self, path: str) -> Optional[Dict]:
        """Get the type of a file or directory"""
        params = {'path': path}
        return self._make_request('/api/v3/filesystem/type', params)
    
    def get_media_files(self, path: str) -> Optional[List[Dict]]:
        """Get media files in the specified path"""
        params = {'path': path}
        return self._make_request('/api/v3/filesystem/mediafiles', params)
    
    def get_episode_files(self, series_id: int = None) -> Optional[List[Dict]]:
        """Get episode files, optionally filtered by series ID"""
        params = {}
        if series_id:
            params['seriesId'] = series_id
        
        return self._make_request('/api/v3/episodefile', params)
    
    def format_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def display_directory_listing(self, items: List[Dict], current_path: str = ""):
        """Display a formatted directory listing"""
        if not items:
            print("No items found.")
            return
        
        print(f"\nDirectory listing for: {current_path or 'Root'}")
        print("-" * 80)
        print(f"{'Type':<6} {'Name':<40} {'Size':<12} {'Path'}")
        print("-" * 80)
        
        # Sort items: directories first, then files
        sorted_items = sorted(items, key=lambda x: (x.get('type', 'file') != 'folder', x.get('name', '')))
        
        for item in sorted_items:
            item_type = item.get('type', 'file')
            name = item.get('name', 'Unknown')
            size = item.get('size', 0)
            path = item.get('path', '')
            
            type_display = "DIR" if item_type == 'folder' else "FILE"
            size_display = self.format_size(size) if item_type != 'folder' else ""
            
            # Truncate long names
            display_name = name[:37] + "..." if len(name) > 40 else name
            
            print(f"{type_display:<6} {display_name:<40} {size_display:<12} {path}")
    
    def interactive_browser(self):
        """Start an interactive file browser session"""
        current_path = ""
        
        print("Sonarr File System Browser")
        print("Commands: ls [path], cd <path>, pwd, media [path], episodes, view <file>, help, quit")
        print("=" * 80)
        
        while True:
            try:
                command = input(f"sonarr:{current_path or '/'}$ ").strip()
                
                if not command:
                    continue
                
                parts = command.split(maxsplit=1)
                cmd = parts[0].lower()
                arg = parts[1] if len(parts) > 1 else None
                
                if cmd in ['quit', 'exit', 'q']:
                    break
                
                elif cmd == 'help':
                    print("\nAvailable commands:")
                    print("  ls [path]     - List directory contents")
                    print("  cd <path>     - Change directory")
                    print("  pwd           - Show current directory")
                    print("  media [path]  - Show media files in path")
                    print("  episodes      - Show all episode files")
                    print("  type <path>   - Show file/directory type")
                    print("  view <file>   - View detailed file information")
                    print("  help          - Show this help")
                    print("  quit/exit/q   - Exit browser")
                
                elif cmd == 'pwd':
                    print(current_path or "/")
                
                elif cmd == 'ls':
                    path = arg if arg else current_path
                    items = self.browse_filesystem(path, include_files=True)
                    if items is not None:
                        self.display_directory_listing(items, path)
                
                elif cmd == 'cd':
                    if not arg:
                        print("Usage: cd <path>")
                        continue
                    
                    # Handle relative paths
                    if arg.startswith('/'):
                        new_path = arg
                    elif arg == '..':
                        if current_path:
                            new_path = os.path.dirname(current_path.rstrip('/'))
                        else:
                            new_path = ""
                    else:
                        new_path = os.path.join(current_path, arg) if current_path else arg
                    
                    # Test if path exists by trying to browse it
                    items = self.browse_filesystem(new_path, include_files=False)
                    if items is not None:
                        current_path = new_path
                        print(f"Changed to: {current_path or '/'}")
                    else:
                        print(f"Cannot access path: {new_path}")
                
                elif cmd == 'media':
                    path = arg if arg else current_path
                    media_files = self.get_media_files(path)
                    if media_files is not None:
                        if media_files:
                            self.display_directory_listing(media_files, f"{path} (media files)")
                        else:
                            print(f"No media files found in: {path or '/'}")
                
                elif cmd == 'episodes':
                    episodes = self.get_episode_files()
                    if episodes is not None:
                        print(f"\nFound {len(episodes)} episode files:")
                        print("-" * 80)
                        print(f"{'ID':<6} {'Series':<20} {'Season':<8} {'Path'}")
                        print("-" * 80)
                        
                        for ep in episodes[:20]:  # Limit to first 20
                            ep_id = ep.get('id', 'N/A')
                            series_id = ep.get('seriesId', 'N/A')
                            season = ep.get('seasonNumber', 'N/A')
                            path = ep.get('path', 'N/A')
                            
                            print(f"{ep_id:<6} {series_id:<20} {season:<8} {path}")
                        
                        if len(episodes) > 20:
                            print(f"... and {len(episodes) - 20} more")
                
                elif cmd == 'type':
                    if not arg:
                        print("Usage: type <path>")
                        continue

                    file_type = self.get_file_type(arg)
                    if file_type is not None:
                        print(f"Type information for '{arg}':")
                        print(json.dumps(file_type, indent=2))

                elif cmd == 'view':
                    if not arg:
                        print("Usage: view <file_path>")
                        continue

                    # Get file information
                    file_type = self.get_file_type(arg)
                    if file_type is not None:
                        print(f"\nFile information for: {arg}")
                        print("-" * 50)
                        for key, value in file_type.items():
                            print(f"{key}: {value}")

                        # If it's a media file, try to get additional info
                        if arg.lower().endswith(('.mkv', '.mp4', '.avi', '.mov', '.wmv', '.flv')):
                            media_files = self.get_media_files(os.path.dirname(arg))
                            if media_files:
                                matching_file = next((f for f in media_files if f.get('path') == arg), None)
                                if matching_file:
                                    print("\nMedia file details:")
                                    print("-" * 30)
                                    for key, value in matching_file.items():
                                        if key not in ['path']:
                                            print(f"{key}: {value}")

                else:
                    print(f"Unknown command: {cmd}. Type 'help' for available commands.")
            
            except KeyboardInterrupt:
                print("\nUse 'quit' to exit.")
            except Exception as e:
                print(f"Error: {e}")


def main():
    if len(sys.argv) < 2:
        print("Usage: python sonarr_file_browser.py <ip_address> [port] [api_key] [--https]")
        print("  --https: Use HTTPS instead of HTTP")
        sys.exit(1)

    host = sys.argv[1]
    port = 8989
    api_key = None
    use_https = False

    # Parse arguments
    args = sys.argv[2:]
    for arg in args:
        if arg == '--https':
            use_https = True
        elif arg.isdigit():
            port = int(arg)
        elif not api_key:
            api_key = arg

    if not api_key:
        api_key = getpass.getpass("Enter Sonarr API key: ")

    browser = SonarrFileBrowser(host, port, api_key, use_https)

    if not browser.test_connection():
        print("Failed to connect to Sonarr. Please check your connection details and API key.")
        print("If using HTTPS, make sure your Sonarr server supports SSL.")
        sys.exit(1)

    browser.interactive_browser()


if __name__ == "__main__":
    main()
