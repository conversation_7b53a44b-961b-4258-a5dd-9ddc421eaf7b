#!/usr/bin/env python3
"""
Test script for Sonarr File Browser

This script tests the basic functionality of the Sonarr File Browser
without requiring user interaction.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sonarr_file_browser import SonarrFileBrowser


def test_sonarr_connection(host, port, api_key):
    """Test basic Sonarr connection and API functionality"""
    print(f"Testing connection to Sonarr at {host}:{port}")
    print("=" * 50)
    
    browser = SonarrFileBrowser(host, port, api_key)
    
    # Test connection
    print("1. Testing connection...")
    if not browser.test_connection():
        print("❌ Connection test failed")
        return False
    print("✅ Connection successful")
    
    # Test filesystem browsing
    print("\n2. Testing filesystem browsing...")
    try:
        items = browser.browse_filesystem(include_files=False)
        if items is not None:
            print(f"✅ Found {len(items)} items in root directory")
            if items:
                print("   Sample directories:")
                for item in items[:3]:
                    name = item.get('name', 'Unknown')
                    item_type = item.get('type', 'unknown')
                    print(f"   - {name} ({item_type})")
        else:
            print("❌ Failed to browse filesystem")
            return False
    except Exception as e:
        print(f"❌ Error browsing filesystem: {e}")
        return False
    
    # Test file type detection
    print("\n3. Testing file type detection...")
    try:
        if items and len(items) > 0:
            test_path = items[0].get('path', '/')
            file_type = browser.get_file_type(test_path)
            if file_type is not None:
                print(f"✅ File type detection works for: {test_path}")
                print(f"   Type: {file_type.get('type', 'unknown')}")
            else:
                print("⚠️  File type detection returned no data")
        else:
            print("⚠️  No items to test file type detection")
    except Exception as e:
        print(f"❌ Error testing file type: {e}")
    
    # Test episode files
    print("\n4. Testing episode file listing...")
    try:
        episodes = browser.get_episode_files()
        if episodes is not None:
            print(f"✅ Found {len(episodes)} episode files")
            if episodes:
                print("   Sample episode files:")
                for ep in episodes[:3]:
                    path = ep.get('path', 'Unknown')
                    series_id = ep.get('seriesId', 'N/A')
                    season = ep.get('seasonNumber', 'N/A')
                    print(f"   - Series {series_id}, Season {season}: {os.path.basename(path)}")
        else:
            print("❌ Failed to get episode files")
    except Exception as e:
        print(f"❌ Error getting episode files: {e}")
    
    print("\n" + "=" * 50)
    print("✅ All basic tests completed successfully!")
    return True


def main():
    if len(sys.argv) < 4:
        print("Usage: python test_sonarr_browser.py <host> <port> <api_key>")
        print("Example: python test_sonarr_browser.py localhost 8989 your_api_key")
        sys.exit(1)
    
    host = sys.argv[1]
    port = int(sys.argv[2])
    api_key = sys.argv[3]
    
    success = test_sonarr_connection(host, port, api_key)
    
    if success:
        print("\n🎉 All tests passed! The Sonarr File Browser should work correctly.")
        print("You can now run: python sonarr_file_browser.py {} {}".format(host, port))
    else:
        print("\n❌ Some tests failed. Please check your Sonarr configuration.")
        sys.exit(1)


if __name__ == "__main__":
    main()
